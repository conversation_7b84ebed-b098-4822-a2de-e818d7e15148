# Capstone Project Evaluation Report

**Student:** James
**Date:** 2025-07-23
**Total Score:** 55/70 points

## Section 1: Frontend (30 points)

### Task 1: CSS Layout Feature Boxes (5 points)

- **Score:** 3/5
- **Level:** Developing
- **Feedback:** Successfully added both required feature boxes ("Progress Tracking" and "Real-time Assessments") with correct titles. However, the structure is incomplete as these boxes only contain titles (h4 elements) without descriptive paragraphs, unlike the example "Adaptive Courses" box which includes both title and description.
- **Evidence:** Lines 76-81 show the two additional boxes with titles but missing descriptive content: `<h4>Progress Tracking</h4>` and `<h4>Real-time Assessments</h4>`

### Task 2: Bootstrap Cards (5 points)

- **Score:** 4/5
- **Level:** Proficient
- **Feedback:** Excellent implementation of Bootstrap grid system with two cards placed side by side. Each card includes all required elements: card class, card-body, card-title, card-text, and button. Minor issue with button having extra unnecessary classes.
- **Evidence:** Lines 84-113 show proper Bootstrap row/col-md-6 structure with complete card components. Button classes include extra attributes: `class="btn btn-primary card-body card-title card-text"` should be just `class="btn btn-primary"`

### Task 3: JavaScript Email Validation (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Perfect implementation of email validation function. Correctly checks for "@" symbol, updates DOM with appropriate messages, and handles form submission properly.
- **Evidence:** Lines 82-94 show complete validateEmail() function with proper logic: `emailInput.includes("@")`, correct message updates, and return true/false for form handling

### Task 4: JavaScript Input Event Handling (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Excellent implementation of real-time input event handling. The code correctly updates the display as the user types using the 'input' event listener.
- **Evidence:** Lines 108-113 show proper event listener setup: `goalInput.addEventListener("input", function() {...})` with correct DOM manipulation

### Task 5: Password Strength Checker (React) (5 points)

- **Score:** 2/5
- **Level:** Below Expectation
- **Feedback:** The password strength logic is correct (checks length >= 6 and contains number using regex), but the component structure doesn't match requirements. Missing input field and "Check Strength" button - component only receives password as prop.
- **Evidence:** PasswordStrength.jsx shows correct logic but wrong structure - should have input field and button, not receive password as prop

### Task 6: Course Description Toggle (React) (5 points)

- **Score:** 3/5
- **Level:** Developing
- **Feedback:** Toggle functionality works correctly with proper state management and conditional rendering. However, button text doesn't change between "Show Description" and "Hide Description" as required.
- **Evidence:** CourseToggle.jsx shows working toggle with `setVisible(!isVisible)` and conditional rendering, but button text remains static: "Show Description"

## Section 2: Backend (10 points)

### Task 7: Express.js POST /enroll API (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Perfect implementation of POST /enroll endpoint. Correctly accepts JSON body, extracts userId and courseId, and returns proper confirmation message.
- **Evidence:** Lines 28-40 in server.js show complete implementation with proper destructuring and JSON response format

### Task 8: Error Handling for Missing Fields (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Excellent error handling implementation. Correctly validates required fields and returns appropriate 400 status code with proper error message.
- **Evidence:** Lines 31-35 show proper validation: `if (!userId || !courseId)` with correct status and message: "Missing userId or courseId in request."

## Section 3: Databases (15 points)

### Task 9: Create Instructors Table & Insert Records (5 points)

- **Score:** 3/5
- **Level:** Developing
- **Feedback:** SQL syntax is correct with proper AUTO_INCREMENT and UNIQUE constraints. However, only 2 instructor records were inserted instead of the required 3.
- **Evidence:** Lines 3-12 show correct table creation and 2 INSERT statements for 'Thanh Dong' and 'James Dong', missing third instructor

### Task 10: Add User + Enroll + JOIN Query (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Excellent completion of all three SQL steps. Properly adds new user, enrolls them in course, and executes correct JOIN query to show enrolled users.
- **Evidence:** Lines 14-24 show complete workflow: user insertion, enrollment, and proper multi-table JOIN with WHERE clause

### Task 11: Create New Entry in MongoDB Database (5 points)

- **Score:** 0/5
- **Level:** Below Expectation
- **Feedback:** No new school entry was created in the MongoDB database. The export file only contains the original 2 schools provided in the requirements.
- **Evidence:** schoolSystem.schools.json contains only original entries: Riverside Public School and Greenwood High School

## Section 4: AI Features (15 points)

### Task 12: Explain How Smart Search Enhances UX (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Comprehensive explanation with clear comparison to regular search bars. Includes practical examples and demonstrates deep understanding of Smart Search benefits in LMS context.
- **Evidence:** Response provides specific example ("learn web design" returning HTML/CSS courses) and covers key features like synonym recognition and real-time suggestions

### Task 13: Describe Role of Frontend, Backend, and Database (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Clear and detailed explanation of each layer's role in full-stack architecture. Demonstrates understanding of how components interact to deliver Smart Search functionality.
- **Evidence:** Response correctly identifies technologies (React, Express, MongoDB) and explains data flow from frontend input to backend processing to database queries

### Task 14: Identify Challenges and Solutions (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Well-reasoned identification of realistic challenges with thoughtful, technically sound solutions. Shows understanding of both technical and user experience considerations.
- **Evidence:** Identifies specific challenges (ambiguous queries, performance, scaling) with concrete solutions (NLP, indexing, caching, metadata)

## Grading Summary

| Section     | Task                   | Score | Max |
| ----------- | ---------------------- | ----- | --- |
| Frontend    | CSS Layout             | 3     | 5   |
| Frontend    | Bootstrap Cards        | 4     | 5   |
| Frontend    | JavaScript Validation  | 5     | 5   |
| Frontend    | React Components       | 5     | 5   |
| Frontend    | Password Strength      | 2     | 5   |
| Frontend    | Course Toggle          | 3     | 5   |
| Backend     | Express.js API         | 5     | 5   |
| Backend     | Error Handling         | 5     | 5   |
| Database    | MySQL Queries          | 3     | 5   |
| Database    | JOIN Query             | 5     | 5   |
| Database    | MongoDB Implementation | 0     | 5   |
| AI Features | Smart Search Analysis  | 5     | 5   |
| AI Features | Architecture Roles     | 5     | 5   |
| AI Features | Challenges & Solutions | 5     | 5   |
| **TOTAL**   |                        | **55** | **70** |

## Overall Assessment

**Strengths:**
- Excellent JavaScript fundamentals with perfect email validation and input event handling
- Strong Express.js backend implementation with proper API design and error handling
- Comprehensive understanding of AI features and full-stack architecture concepts
- Good SQL skills demonstrated in JOIN queries and database operations
- Well-structured written responses showing deep conceptual understanding

**Areas for Improvement:**
- React component structure needs attention - ensure components match UI requirements (input fields, buttons)
- Complete all required elements in HTML/CSS tasks (descriptive text for feature boxes)
- Follow through on all database tasks (MongoDB entry creation was missed)
- Pay attention to dynamic UI elements (button text changes in toggle components)

**Recommendations:**
1. Review React component requirements carefully and ensure all UI elements are included
2. Test React components to verify they match the specified functionality
3. Complete the MongoDB task by adding a new school entry with required fields
4. Add descriptive paragraphs to the flexbox feature boxes for complete implementation
5. Implement button text changes in the React toggle component

**Files Evaluated:**
- test/Capstone_Section1_HTML_James.html
- test/Capstone_Section1_JS_James.html  
- test/Capstone_Section1_React_James/src/components/PasswordStrength.jsx
- test/Capstone_Section1_React_James/src/components/CourseToggle.jsx
- test/Capstone_Section2_James/server.js
- test/Capstone_Section3_SQL_James.md
- test/Capstone_Section3_James/export/schoolSystem.schools.json
- test/Capstone_Section4_James.md
